#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASM漏洞扫描报告生成器
从asm.md文件中提取数据并生成格式化的漏洞扫描报告
"""

import re
from collections import defaultdict
from urllib.parse import urlparse
import sys

def extract_host_from_url(url):
    """从URL中提取主机名"""
    try:
        parsed = urlparse(url)
        return parsed.netloc if parsed.netloc else parsed.path.split('/')[0]
    except:
        return url

def extract_cve_numbers(vulnerability_name):
    """从漏洞名称中提取CVE编号"""
    cve_pattern = r'CVE-\d{4}-\d{4,7}'
    return re.findall(cve_pattern, vulnerability_name)

def categorize_vulnerability(vulnerability_name):
    """根据漏洞名称对漏洞进行分类"""
    name_lower = vulnerability_name.lower()
    
    # SQL注入类
    if 'sql' in name_lower and ('injection' in name_lower or '注入' in name_lower):
        return 'SQL注入'
    
    # XSS类
    if any(keyword in name_lower for keyword in ['cross-site scripting', 'xss', '跨站脚本']):
        return 'XSS跨站脚本'
    
    # TLS/SSL类
    if any(keyword in name_lower for keyword in ['tls', 'ssl', 'transportation security protocol']):
        return 'TLS/SSL安全'
    
    # JavaScript库类
    if any(keyword in name_lower for keyword in ['jquery', 'bootstrap', 'moment.js']):
        return 'JavaScript库漏洞'
    
    # 配置类
    if any(keyword in name_lower for keyword in ['hsts', 'csp', 'cookie', 'secure attribute', '配置']):
        return '安全配置问题'
    
    # IIS相关
    if 'iis' in name_lower:
        return 'IIS服务器漏洞'
    
    # WebDAV相关
    if 'webdav' in name_lower:
        return 'WebDAV协议漏洞'
    
    # 命令注入
    if any(keyword in name_lower for keyword in ['command injection', '命令注入', 'remote code execution']):
        return '命令注入'
    
    # 文件相关
    if any(keyword in name_lower for keyword in ['file', 'disclosure', '文件', '泄露']):
        return '文件泄露'
    
    # 认证相关
    if any(keyword in name_lower for keyword in ['auth', 'login', 'ntlm', '认证', '登录']):
        return '认证相关'
    
    # WordPress相关
    if 'wordpress' in name_lower or 'wp-' in name_lower:
        return 'WordPress漏洞'
    
    # Joomla相关
    if 'joomla' in name_lower:
        return 'Joomla漏洞'
    
    # GitLab相关
    if 'gitlab' in name_lower:
        return 'GitLab漏洞'
    
    # 权限相关
    if any(keyword in name_lower for keyword in ['privilege', 'permission', '权限', '绕过']):
        return '权限绕过'
    
    # 反序列化
    if '反序列化' in name_lower or 'deserialization' in name_lower:
        return '反序列化漏洞'
    
    # 目录遍历
    if any(keyword in name_lower for keyword in ['directory', 'traversal', '目录', '遍历']):
        return '目录遍历'
    
    # 信息泄露
    if any(keyword in name_lower for keyword in ['information', 'detect', 'disclosure', '信息']):
        return '信息泄露'
    
    # 默认分类
    return '其他安全问题'

def parse_asm_data(file_path):
    """解析asm.md文件数据"""
    vulnerabilities = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return []
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('|----') or line.startswith('| 严重 | CVE-2017-7269'):
            continue
        
        # 解析表格行
        if line.startswith('|') and line.endswith('|'):
            parts = [part.strip() for part in line.split('|')[1:-1]]
            if len(parts) >= 3:
                severity = parts[0].strip()
                vulnerability_name = parts[1].strip()
                url = parts[2].strip()
                
                if severity and vulnerability_name and url:
                    host = extract_host_from_url(url)
                    cve_numbers = extract_cve_numbers(vulnerability_name)
                    category = categorize_vulnerability(vulnerability_name)
                    
                    vulnerabilities.append({
                        'severity': severity,
                        'name': vulnerability_name,
                        'url': url,
                        'host': host,
                        'cve_numbers': cve_numbers,
                        'category': category
                    })
    
    return vulnerabilities

def generate_report(vulnerabilities):
    """生成漏洞扫描报告"""
    if not vulnerabilities:
        return "没有找到漏洞数据"
    
    # 统计数据
    host_stats = defaultdict(lambda: {'total': 0, '严重': 0, '高危': 0, '中危': 0, '低危': 0, '信息': 0})
    vulnerability_stats = defaultdict(lambda: {'hosts': set(), 'count': 0, 'cves': set(), 'urls': []})
    
    # 处理数据
    for vuln in vulnerabilities:
        host = vuln['host']
        severity = vuln['severity']
        category = vuln['category']
        
        # 统计主机漏洞
        host_stats[host]['total'] += 1
        if severity in host_stats[host]:
            host_stats[host][severity] += 1
        
        # 统计漏洞类型
        vulnerability_stats[category]['hosts'].add(host)
        vulnerability_stats[category]['count'] += 1
        vulnerability_stats[category]['cves'].update(vuln['cve_numbers'])
        vulnerability_stats[category]['urls'].append(vuln['url'])
    
    # 生成报告
    report = "# ASM漏洞扫描报告\n\n"
    
    # 表格1：站点漏洞概览表
    report += "## 1. 站点漏洞概览表\n\n"
    report += "| 序号 | 站点URL | 漏洞总数 | 严重 | 高危 | 中危 | 低危 | 信息 |\n"
    report += "|------|---------|----------|------|------|------|------|------|\n"
    
    # 按漏洞总数排序
    sorted_hosts = sorted(host_stats.items(), key=lambda x: x[1]['total'], reverse=True)
    for i, (host, stats) in enumerate(sorted_hosts, 1):
        report += f"| {i} | {host} | {stats['total']} | {stats['严重']} | {stats['高危']} | {stats['中危']} | {stats['低危']} | {stats['信息']} |\n"
    
    # 表格2：站点详细漏洞清单
    report += "\n## 2. 站点详细漏洞清单\n\n"
    
    for host, _ in sorted_hosts:
        host_vulns = [v for v in vulnerabilities if v['host'] == host]
        if host_vulns:
            report += f"### {host}\n\n"
            report += "| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |\n"
            report += "|----------|----------|----------|---------|----------|\n"
            
            # 按漏洞名称分组统计
            vuln_groups = defaultdict(list)
            for vuln in host_vulns:
                vuln_groups[vuln['name']].append(vuln)
            
            for vuln_name, vuln_list in vuln_groups.items():
                count = len(vuln_list)
                severity = vuln_list[0]['severity']
                cves = set()
                urls = []
                for v in vuln_list:
                    cves.update(v['cve_numbers'])
                    urls.append(v['url'])
                
                cve_str = ', '.join(sorted(cves)) if cves else '-'
                url_str = urls[0] if len(urls) == 1 else f"{urls[0]} 等{len(urls)}个地址"
                
                report += f"| {vuln_name} | {count} | {severity} | {cve_str} | {url_str} |\n"
            
            report += "\n"
    
    # 表格3：严重+高危漏洞统计表
    report += "## 3. 严重+高危漏洞统计表\n\n"
    report += "| 漏洞类型 | 影响站点数 | 漏洞总数 | 主要CVE编号 | 代表性站点 |\n"
    report += "|----------|------------|----------|-------------|------------|\n"
    
    high_critical_vulns = defaultdict(lambda: {'hosts': set(), 'count': 0, 'cves': set(), 'urls': []})
    for vuln in vulnerabilities:
        if vuln['severity'] in ['严重', '高危']:
            category = vuln['category']
            high_critical_vulns[category]['hosts'].add(vuln['host'])
            high_critical_vulns[category]['count'] += 1
            high_critical_vulns[category]['cves'].update(vuln['cve_numbers'])
            high_critical_vulns[category]['urls'].append(vuln['url'])
    
    # 按漏洞总数排序
    sorted_high_critical = sorted(high_critical_vulns.items(), key=lambda x: x[1]['count'], reverse=True)
    for category, stats in sorted_high_critical:
        host_count = len(stats['hosts'])
        cve_str = ', '.join(sorted(list(stats['cves']))[:3]) if stats['cves'] else '-'
        if len(stats['cves']) > 3:
            cve_str += f" 等{len(stats['cves'])}个"
        representative_host = list(stats['hosts'])[0] if stats['hosts'] else '-'
        
        report += f"| {category} | {host_count} | {stats['count']} | {cve_str} | {representative_host} |\n"
    
    # 表格4：中危+低危+信息类漏洞统计表
    report += "\n## 4. 中危+低危+信息类漏洞统计表\n\n"
    report += "| 漏洞类型 | 影响站点数 | 漏洞总数 | 主要CVE编号 | 代表性站点 |\n"
    report += "|----------|------------|----------|-------------|------------|\n"
    
    medium_low_info_vulns = defaultdict(lambda: {'hosts': set(), 'count': 0, 'cves': set(), 'urls': []})
    for vuln in vulnerabilities:
        if vuln['severity'] in ['中危', '低危', '信息']:
            category = vuln['category']
            medium_low_info_vulns[category]['hosts'].add(vuln['host'])
            medium_low_info_vulns[category]['count'] += 1
            medium_low_info_vulns[category]['cves'].update(vuln['cve_numbers'])
            medium_low_info_vulns[category]['urls'].append(vuln['url'])
    
    # 按漏洞总数排序
    sorted_medium_low_info = sorted(medium_low_info_vulns.items(), key=lambda x: x[1]['count'], reverse=True)
    for category, stats in sorted_medium_low_info:
        host_count = len(stats['hosts'])
        cve_str = ', '.join(sorted(list(stats['cves']))[:3]) if stats['cves'] else '-'
        if len(stats['cves']) > 3:
            cve_str += f" 等{len(stats['cves'])}个"
        representative_host = list(stats['hosts'])[0] if stats['hosts'] else '-'
        
        report += f"| {category} | {host_count} | {stats['count']} | {cve_str} | {representative_host} |\n"
    
    # 添加统计摘要
    total_hosts = len(host_stats)
    total_vulns = len(vulnerabilities)
    total_cves = len(set(cve for vuln in vulnerabilities for cve in vuln['cve_numbers']))
    
    report += f"\n## 统计摘要\n\n"
    report += f"- **扫描主机总数**: {total_hosts}\n"
    report += f"- **漏洞总数**: {total_vulns}\n"
    report += f"- **CVE总数**: {total_cves}\n"
    
    severity_counts = defaultdict(int)
    for vuln in vulnerabilities:
        severity_counts[vuln['severity']] += 1
    
    report += f"- **严重漏洞**: {severity_counts['严重']}\n"
    report += f"- **高危漏洞**: {severity_counts['高危']}\n"
    report += f"- **中危漏洞**: {severity_counts['中危']}\n"
    report += f"- **低危漏洞**: {severity_counts['低危']}\n"
    report += f"- **信息类**: {severity_counts['信息']}\n"
    
    return report

def main():
    """主函数"""
    input_file = 'asm.md'
    output_file = 'vulnerability_report.md'
    
    print("正在解析ASM漏洞数据...")
    vulnerabilities = parse_asm_data(input_file)
    
    if not vulnerabilities:
        print("未找到有效的漏洞数据")
        return
    
    print(f"成功解析 {len(vulnerabilities)} 条漏洞记录")
    
    print("正在生成漏洞报告...")
    report = generate_report(vulnerabilities)
    
    # 保存报告
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"漏洞报告已生成: {output_file}")

if __name__ == "__main__":
    main()
