#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接生成AWVS漏洞扫描报告
"""

import json
import re
from collections import defaultdict
from datetime import datetime

# 严重等级映射
severity_map = {
    4: "严重",
    3: "高危", 
    2: "中危",
    1: "低危",
    0: "信息"
}

def extract_cve_numbers(text):
    """从文本中提取CVE编号"""
    if not text:
        return []
    cve_pattern = r'CVE-\d{4}-\d{4,7}'
    return re.findall(cve_pattern, str(text), re.IGNORECASE)

def classify_vulnerability_type(vuln_name, description="", tags=None):
    """根据漏洞名称和描述分类漏洞类型"""
    vuln_name = vuln_name.lower()
    description = description.lower() if description else ""
    tags = [tag.lower() for tag in (tags or [])]
    
    # SQL注入类
    if any(keyword in vuln_name for keyword in ['sql injection', 'sql注入']):
        return "SQL注入"
    
    # XSS类
    if any(keyword in vuln_name for keyword in ['cross-site scripting', 'xss', '跨站脚本']):
        return "跨站脚本(XSS)"
    
    # TLS/SSL类
    if any(keyword in vuln_name for keyword in ['tls', 'ssl', 'cipher', 'certificate', 'https']):
        return "TLS/SSL配置"
    
    # GraphQL类
    if 'graphql' in vuln_name:
        return "GraphQL配置"
    
    # JavaScript库类
    if any(keyword in vuln_name for keyword in ['javascript', 'jquery', 'bootstrap', 'moment.js', 'library', 'outdated']):
        return "JavaScript库"
    
    # 配置类
    if any(keyword in vuln_name for keyword in ['csp', 'content security policy', 'permissions-policy', 'hsts', 'cookie']):
        return "安全配置"
    
    # HTTP配置类
    if any(keyword in vuln_name for keyword in ['http', 'redirection', 'insecure']):
        return "HTTP配置"
    
    # 信息泄露类
    if any(keyword in vuln_name for keyword in ['information disclosure', 'prometheus', 'metrics', '信息泄露']):
        return "信息泄露"
    
    # CSRF类
    if 'csrf' in vuln_name or 'cross-site request forgery' in vuln_name:
        return "跨站请求伪造(CSRF)"
    
    # 拒绝服务类
    if any(keyword in vuln_name for keyword in ['denial of service', 'dos', 'overloading']):
        return "拒绝服务(DoS)"
    
    # 默认分类
    return "其他安全问题"

# 文件路径
json_file = "awvs/20250730_JSON_Multiple_targets.json"
output_file = "awvs_vulnerability_report.md"

print(f"开始解析文件: {json_file}")

# 加载JSON数据
try:
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    print("JSON文件加载成功")
except Exception as e:
    print(f"加载JSON文件失败: {e}")
    exit(1)

# 解析数据
sites_data = []
vulnerability_stats = defaultdict(lambda: {
    'sites': set(),
    'count': 0,
    'severity': '',
    'cves': set(),
    'urls': set()
})

scans = data['export']['scans']
print(f"找到 {len(scans)} 个扫描站点")

for scan in scans:
    site_info = {
        'host': scan['info']['host'],
        'start_url': scan['info']['start_url'],
        'duration': scan['info']['duration'],
        'vulnerabilities': [],
        'vuln_count_by_severity': {
            '严重': 0, '高危': 0, '中危': 0, '低危': 0, '信息': 0
        },
        'total_vulns': 0
    }
    
    # 处理漏洞类型
    vuln_types = {vt['vt_id']: vt for vt in scan.get('vulnerability_types', [])}
    
    # 处理具体漏洞实例
    for vuln in scan.get('vulnerabilities', []):
        vuln_info = vuln['info']
        vt_id = vuln_info['vt_id']
        
        if vt_id in vuln_types:
            vt = vuln_types[vt_id]
            severity_num = vt['severity']
            severity_cn = severity_map.get(severity_num, "未知")
            
            # 提取CVE编号
            cves = []
            for field in ['description', 'recommendation', 'long_description']:
                if field in vt:
                    cves.extend(extract_cve_numbers(vt[field]))
            
            # 从tags中提取CVE
            if 'tags' in vt:
                for tag in vt['tags']:
                    cves.extend(extract_cve_numbers(tag))
            
            vuln_detail = {
                'name': vt['name'],
                'severity': severity_cn,
                'severity_num': severity_num,
                'url': vuln_info['url'],
                'cves': list(set(cves)),
                'cvss_score': vt.get('cvss_score', 0),
                'description': vt.get('description', ''),
                'tags': vt.get('tags', [])
            }
            
            site_info['vulnerabilities'].append(vuln_detail)
            site_info['vuln_count_by_severity'][severity_cn] += 1
            site_info['total_vulns'] += 1
            
            # 统计全局漏洞类型
            vuln_type = classify_vulnerability_type(
                vt['name'], vt.get('description', ''), vt.get('tags', [])
            )
            
            vulnerability_stats[vuln_type]['sites'].add(site_info['host'])
            vulnerability_stats[vuln_type]['count'] += 1
            vulnerability_stats[vuln_type]['severity'] = severity_cn
            vulnerability_stats[vuln_type]['cves'].update(cves)
            vulnerability_stats[vuln_type]['urls'].add(vuln_info['url'])
    
    sites_data.append(site_info)

# 按漏洞总数排序
sites_data.sort(key=lambda x: x['total_vulns'], reverse=True)

print(f"解析完成，共处理 {len(sites_data)} 个站点")

# 生成报告
report = []

# 报告标题
report.append("# AWVS漏洞扫描报告")
report.append("")
report.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
report.append(f"**扫描站点数**: {len(sites_data)}")

total_vulns = sum(site['total_vulns'] for site in sites_data)
report.append(f"**漏洞总数**: {total_vulns}")
report.append("")

# 表格1：站点漏洞概览表
report.append("## 1. 站点漏洞概览表")
report.append("")
report.append("| 序号 | 站点URL | 漏洞总数 | 严重 | 高危 | 中危 | 低危 | 信息 |")
report.append("|------|---------|----------|------|------|------|------|------|")

for i, site in enumerate(sites_data, 1):
    counts = site['vuln_count_by_severity']
    report.append(f"| {i} | {site['host']} | {site['total_vulns']} | {counts['严重']} | {counts['高危']} | {counts['中危']} | {counts['低危']} | {counts['信息']} |")

report.append("")

print("生成站点概览表完成")

# 保存第一部分报告
try:
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("\n".join(report))
    print(f"报告第一部分已保存到: {output_file}")
except Exception as e:
    print(f"保存报告失败: {e}")
    exit(1)

print("脚本执行完成！")
