# AWVS漏洞扫描报告

**生成时间**: 2025-08-12 10:17:21
**扫描站点数**: 20
**漏洞总数**: 397

## 1. 站点漏洞概览表

| 序号 | 站点URL | 漏洞总数 | 严重 | 高危 | 中危 | 低危 | 信息 |
|------|---------|----------|------|------|------|------|------|
| 1 | gazimagusa.gov.ct.tr | 123 | 5 | 55 | 48 | 8 | 7 |
| 2 | www.tvet-pal.pna.ps | 54 | 0 | 3 | 19 | 23 | 9 |
| 3 | www.cwa.pna.ps | 44 | 0 | 4 | 20 | 12 | 8 |
| 4 | mowa.pna.ps | 34 | 0 | 5 | 2 | 23 | 4 |
| 5 | enext.iptime.org:20080 | 28 | 0 | 16 | 7 | 2 | 3 |
| 6 | mpwh.pna.ps | 27 | 2 | 2 | 8 | 6 | 9 |
| 7 | coding.iptime.org | 22 | 0 | 1 | 11 | 4 | 6 |
| 8 | formations.molg.pna.ps | 13 | 1 | 1 | 3 | 5 | 3 |
| 9 | gmdh.gov.ct.tr | 9 | 0 | 1 | 3 | 3 | 2 |
| 10 | trc.synology.me:8000 | 7 | 0 | 0 | 2 | 3 | 2 |
| 11 | tspdj.iptime.org:8110 | 7 | 0 | 0 | 3 | 2 | 2 |
| 12 | yne.iptime.org:8000 | 7 | 0 | 0 | 3 | 2 | 2 |
| 13 | spsun1.iptime.org:8083 | 6 | 0 | 0 | 2 | 1 | 3 |
| 14 | ocpu.survey.stat.gov.ct.tr | 6 | 0 | 0 | 3 | 0 | 3 |
| 15 | spsun.iptime.org:8117 | 4 | 0 | 0 | 2 | 0 | 2 |
| 16 | tasdik.maliye.gov.ct.tr | 4 | 0 | 0 | 1 | 0 | 3 |
| 17 | www.ssd.gov.ct.tr | 2 | 0 | 0 | 1 | 1 | 0 |
| 18 | cyeng.iptime.org:861 | 0 | 0 | 0 | 0 | 0 | 0 |
| 19 | gsfmc-webdc.iptime.org:31080 | 0 | 0 | 0 | 0 | 0 | 0 |
| 20 | wow2020.iptime.org | 0 | 0 | 0 | 0 | 0 | 0 |

## 2. 站点详细漏洞清单

### gazimagusa.gov.ct.tr

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| Joomla CVE-2021-23127 Vulnerability | 1 | 严重 | CVE-2021-23127 | https://gazimagusa.gov.ct.tr/ |
| Joomla CVE-2021-23128 Vulnerability | 1 | 严重 | CVE-2021-23128 | https://gazimagusa.gov.ct.tr/ |
| Joomla Improper Authentication Vulnerability | 1 | 严重 | CVE-2022-23795 | https://gazimagusa.gov.ct.tr/ |
| Joomla Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') Vulnerability | 6 | 高危 | CVE-2008-6852, CVE-2009-1499, CVE-2010-2679, CVE-2015-4654, CVE-2020-35613, CVE-2022-23797 | https://gazimagusa.gov.ct.tr/ |
| Joomla CVE-2020-35610 Vulnerability | 1 | 高危 | CVE-2020-35610 | https://gazimagusa.gov.ct.tr/ |
| Joomla CVE-2021-23132 Vulnerability | 1 | 高危 | CVE-2021-23132 | https://gazimagusa.gov.ct.tr/ |
| Joomla CVE-2023-40626 Vulnerability | 1 | 高危 | CVE-2023-40626 | https://gazimagusa.gov.ct.tr/ |
| Joomla Exposure of Sensitive Information to an Unauthorized Actor Vulnerability | 3 | 中危 | CVE-2020-15698, CVE-2020-35611, CVE-2020-35614 | https://gazimagusa.gov.ct.tr/ |
| Joomla Improper Check for Unusual or Exceptional Conditions Vulnerability | 1 | 高危 | CVE-2021-26038 | https://gazimagusa.gov.ct.tr/ |
| Joomla Improper Input Validation Vulnerability | 5 | 中危 | CVE-2006-1957, CVE-2020-35616, CVE-2021-23131, CVE-2021-26029, CVE-2021-26036 | https://gazimagusa.gov.ct.tr/ |
| Joomla Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal') Vulnerability | 3 | 中危 | CVE-2020-35612, CVE-2021-26028, CVE-2022-23793 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core 3.9.x Cross-Site Request Forgery | 2 | 高危 | CVE-2020-15695, CVE-2020-35615 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core 3.9.x Cross-Site Scripting | 2 | 高危 | CVE-2020-24599, CVE-2021-23124 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core 3.9.x Information Disclosure | 1 | 高危 | CVE-2020-35614 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core 3.x.x Cross-Site Request Forgery | 3 | 高危 | CVE-2020-15700, CVE-2021-26033, CVE-2021-26034 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core 3.x.x Cross-Site Scripting | 7 | 高危 | CVE-2020-15696, CVE-2021-23125, CVE-2021-26030, CVE-2021-26032, CVE-2021-26035, CVE-2021-26039, CVE-2022-23796 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core 3.x.x Directory Traversal | 2 | 高危 | CVE-2021-23132, CVE-2021-26028 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core 3.x.x Information Disclosure | 1 | 高危 | CVE-2020-15698 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core 3.x.x Local File Inclusion | 1 | 高危 | CVE-2021-26031 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core 3.x.x Multiple Vulnerabilities | 1 | 高危 | CVE-2022-23793, CVE-2022-23794, CVE-2022-23797 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core 3.x.x Open Redirect | 1 | 高危 | CVE-2020-24598 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core 3.x.x SQL Injection | 1 | 高危 | CVE-2020-35613 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core 3.x.x Security Bypass | 6 | 高危 | CVE-2020-15697, CVE-2021-23123, CVE-2021-23126, CVE-2021-23127, CVE-2021-23128, CVE-2021-23131, CVE-2021-26027 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core Cross-Site Scripting | 2 | 高危 | CVE-2021-23129, CVE-2021-23130 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core Denial of Service | 1 | 高危 | CVE-2021-26036 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core Directory Traversal | 2 | 高危 | CVE-2020-24597, CVE-2020-35612 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core Information Disclosure | 2 | 高危 | CVE-2020-35610, CVE-2020-35611 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core Multiple Vulnerabilities | 1 | 高危 | CVE-2022-23795, CVE-2022-23798 | https://gazimagusa.gov.ct.tr/ |
| Joomla! Core Security Bypass | 5 | 高危 | CVE-2020-15699, CVE-2020-35616, CVE-2021-26029, CVE-2021-26037, CVE-2021-26038 | https://gazimagusa.gov.ct.tr/ |
| Bootstrap Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') Vulnerability | 6 | 中危 | CVE-2016-10735, CVE-2018-14040, CVE-2018-14041, CVE-2018-14042, CVE-2018-20676, CVE-2018-20677, CVE-2019-8331 | https://gazimagusa.gov.ct.tr/ |
| HTTP Strict Transport Security (HSTS) Policy Not Enabled | 1 | 中危 | - | https://gazimagusa.gov.ct.tr/ |
| Joomla CVE-2021-26031 Vulnerability | 1 | 中危 | CVE-2021-26031 | https://gazimagusa.gov.ct.tr/ |
| Joomla Cross-Site Request Forgery (CSRF) | 1 | 中危 | CVE-2021-26033 | https://gazimagusa.gov.ct.tr/ |
| Joomla Cross-Site Request Forgery (CSRF) Vulnerability | 4 | 中危 | CVE-2020-15695, CVE-2020-15700, CVE-2020-35615, CVE-2021-26034 | https://gazimagusa.gov.ct.tr/ |
| Joomla Generation of Error Message Containing Sensitive Information Vulnerability | 1 | 中危 | CVE-2022-23794 | https://gazimagusa.gov.ct.tr/ |
| Joomla Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') Vulnerability | 14 | 中危 | CVE-2020-15696, CVE-2020-24599, CVE-2021-23124, CVE-2021-23125, CVE-2021-23129, CVE-2021-23130, CVE-2021-26030, CVE-2021-26032, CVE-2021-26035, CVE-2021-26039, CVE-2022-23796, CVE-2024-21731, CVE-2024-26278, CVE-2024-26279 | https://gazimagusa.gov.ct.tr/ |
| Joomla Inadequate Encryption Strength Vulnerability | 1 | 中危 | CVE-2021-23126 | https://gazimagusa.gov.ct.tr/ |
| Joomla Incorrect Authorization Vulnerability | 1 | 中危 | CVE-2021-26027 | https://gazimagusa.gov.ct.tr/ |
| Joomla Incorrect Permission Assignment for Critical Resource Vulnerability | 1 | 中危 | CVE-2020-15697 | https://gazimagusa.gov.ct.tr/ |
| Joomla Insufficient Session Expiration Vulnerability | 1 | 中危 | CVE-2021-26037 | https://gazimagusa.gov.ct.tr/ |
| Joomla Insufficient Verification of Data Authenticity Vulnerability | 1 | 中危 | CVE-2020-15699 | https://gazimagusa.gov.ct.tr/ |
| Joomla Missing Authorization Vulnerability | 1 | 中危 | CVE-2021-23123 | https://gazimagusa.gov.ct.tr/ |
| Joomla URL Redirection to Untrusted Site ('Open Redirect') Vulnerability | 2 | 中危 | CVE-2020-24598, CVE-2022-23798 | https://gazimagusa.gov.ct.tr/ |
| SSL Certificate Is About To Expire | 1 | 中危 | - | https://gazimagusa.gov.ct.tr/ |
| Vulnerable JavaScript libraries | 1 | 中危 | - | https://gazimagusa.gov.ct.tr/ |
| [Possible] Backup Folder | 1 | 中危 | - | https://gazimagusa.gov.ct.tr/modules/mod_bt_contentslider%20-%20Copy/ |
| jQuery Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') Vulnerability | 3 | 中危 | CVE-2015-9251, CVE-2020-11022, CVE-2020-11023 | https://gazimagusa.gov.ct.tr/ |
| jQuery Improperly Controlled Modification of Object Prototype Attributes ('Prototype Pollution') Vulnerability | 1 | 中危 | CVE-2019-11358 | https://gazimagusa.gov.ct.tr/ |
| Cookies Not Marked as Secure | 1 | 低危 | - | https://gazimagusa.gov.ct.tr/ |
| Cookies with missing, inconsistent or contradictory properties | 1 | 低危 | - | https://gazimagusa.gov.ct.tr/ |
| Documentation files | 1 | 低危 | - | https://gazimagusa.gov.ct.tr/ |
| Insecure Frame (External) | 2 | 低危 | - | https://gazimagusa.gov.ct.tr/, https://gazimagusa.gov.ct.tr/component/content/category/79-blog |
| Possible sensitive directories | 1 | 低危 | - | https://gazimagusa.gov.ct.tr/ |
| Programming Error Messages | 1 | 低危 | - | https://gazimagusa.gov.ct.tr/ |
| Version Disclosure (PHP) | 1 | 低危 | - | https://gazimagusa.gov.ct.tr/ |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | https://gazimagusa.gov.ct.tr/ |
| Generic Email Address Disclosure | 1 | 信息 | - | https://gazimagusa.gov.ct.tr/ |
| Outdated JavaScript libraries | 2 | 信息 | - | https://gazimagusa.gov.ct.tr/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | https://gazimagusa.gov.ct.tr/ |
| Reverse Proxy Detected | 1 | 信息 | - | https://gazimagusa.gov.ct.tr/ |
| Subresource Integrity (SRI) Not Implemented | 1 | 信息 | - | https://gazimagusa.gov.ct.tr/ |

### www.tvet-pal.pna.ps

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| jQuery Validation Other Vulnerability | 2 | 高危 | CVE-2021-43306, CVE-2022-31147 | https://www.tvet-pal.pna.ps/ |
| jQuery Validation Uncontrolled Resource Consumption Vulnerability | 1 | 高危 | CVE-2021-21252 | https://www.tvet-pal.pna.ps/ |
| Active Mixed Content over HTTPS | 1 | 中危 | - | https://www.tvet-pal.pna.ps/operating_boards |
| Bootstrap Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') Vulnerability | 6 | 中危 | CVE-2016-10735, CVE-2018-14040, CVE-2018-14041, CVE-2018-14042, CVE-2018-20676, CVE-2018-20677, CVE-2019-8331 | https://www.tvet-pal.pna.ps/ |
| Development configuration files | 1 | 中危 | - | https://www.tvet-pal.pna.ps/ |
| HTTP Strict Transport Security (HSTS) Policy Not Enabled | 1 | 中危 | - | https://www.tvet-pal.pna.ps/ |
| SSL Certificate Name Hostname Mismatch | 1 | 中危 | - | https://www.tvet-pal.pna.ps/ |
| Stack Trace Disclosure (Laravel) | 1 | 中危 | - | https://www.tvet-pal.pna.ps/ |
| TLS/SSL Weak Cipher Suites | 1 | 中危 | - | https://www.tvet-pal.pna.ps/ |
| Vulnerable JavaScript libraries | 2 | 中危 | - | https://www.tvet-pal.pna.ps/ |
| jQuery Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') Vulnerability | 4 | 中危 | CVE-2015-9251, CVE-2020-11022, CVE-2020-11023, CVE-2020-23064 | https://www.tvet-pal.pna.ps/ |
| jQuery Improperly Controlled Modification of Object Prototype Attributes ('Prototype Pollution') Vulnerability | 1 | 中危 | CVE-2019-11358 | https://www.tvet-pal.pna.ps/ |
| Composer installed.json publicly accessible | 1 | 低危 | - | https://www.tvet-pal.pna.ps/vendor/ |
| Cookies Not Marked as HttpOnly | 1 | 低危 | - | https://www.tvet-pal.pna.ps/ |
| Cookies Not Marked as Secure | 1 | 低危 | - | https://www.tvet-pal.pna.ps/ |
| Cookies with missing, inconsistent or contradictory properties | 1 | 低危 | - | https://www.tvet-pal.pna.ps/ |
| Insecure Frame (External) | 2 | 低危 | - | https://www.tvet-pal.pna.ps/albums/44, https://www.tvet-pal.pna.ps/index.php/ads/13 |
| Passive Mixed Content over HTTPS | 1 | 低危 | - | https://www.tvet-pal.pna.ps/structure |
| Programming Error Messages | 1 | 低危 | - | https://www.tvet-pal.pna.ps/ |
| Symfony debug mode enabled | 14 | 低危 | - | https://www.tvet-pal.pna.ps/ads/23, https://www.tvet-pal.pna.ps/ads/24, https://www.tvet-pal.pna.ps/... |
| Version Disclosure (IIS) | 1 | 低危 | - | https://www.tvet-pal.pna.ps/ |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | https://www.tvet-pal.pna.ps/ |
| Generic Email Address Disclosure | 1 | 信息 | - | https://www.tvet-pal.pna.ps/ |
| Outdated JavaScript libraries | 3 | 信息 | - | https://www.tvet-pal.pna.ps/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | https://www.tvet-pal.pna.ps/ |
| Subresource Integrity (SRI) Not Implemented | 1 | 信息 | - | https://www.tvet-pal.pna.ps/ |
| Web Application Firewall Detected | 1 | 信息 | - | https://www.tvet-pal.pna.ps/ |
| [Possible] Internal Path Disclosure (Windows) | 1 | 信息 | - | https://www.tvet-pal.pna.ps/ |

### www.cwa.pna.ps

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| Insecure Transportation Security Protocol Supported (TLS 1.0) | 1 | 高危 | - | https://www.cwa.pna.ps/ |
| jQuery Validation Other Vulnerability | 2 | 高危 | CVE-2021-43306, CVE-2022-31147 | https://www.cwa.pna.ps/ |
| jQuery Validation Uncontrolled Resource Consumption Vulnerability | 1 | 高危 | CVE-2021-21252 | https://www.cwa.pna.ps/ |
| Active Mixed Content over HTTPS | 1 | 中危 | - | https://www.cwa.pna.ps/ |
| Bootstrap Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') Vulnerability | 6 | 中危 | CVE-2016-10735, CVE-2018-14040, CVE-2018-14041, CVE-2018-14042, CVE-2018-20676, CVE-2018-20677, CVE-2019-8331 | https://www.cwa.pna.ps/ |
| Development configuration files | 1 | 中危 | - | https://www.cwa.pna.ps/ |
| HTTP Strict Transport Security (HSTS) Policy Not Enabled | 1 | 中危 | - | https://www.cwa.pna.ps/ |
| Host header attack | 1 | 中危 | - | https://www.cwa.pna.ps/laws |
| SSL Certificate Name Hostname Mismatch | 1 | 中危 | - | https://www.cwa.pna.ps/ |
| Stack Trace Disclosure (Laravel) | 1 | 中危 | - | https://www.cwa.pna.ps/ |
| TLS/SSL Sweet32 attack | 1 | 中危 | CVE-2016-2183, CVE-2016-6329 | https://www.cwa.pna.ps/ |
| TLS/SSL Weak Cipher Suites | 1 | 中危 | - | https://www.cwa.pna.ps/ |
| Test CGI script leaking environment variables | 1 | 中危 | - | https://www.cwa.pna.ps/ |
| Vulnerable JavaScript libraries | 1 | 中危 | - | https://www.cwa.pna.ps/ |
| jQuery Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') Vulnerability | 3 | 中危 | CVE-2015-9251, CVE-2020-11022, CVE-2020-11023 | https://www.cwa.pna.ps/ |
| jQuery Improperly Controlled Modification of Object Prototype Attributes ('Prototype Pollution') Vulnerability | 1 | 中危 | CVE-2019-11358 | https://www.cwa.pna.ps/ |
| Cookies Not Marked as HttpOnly | 1 | 低危 | - | https://www.cwa.pna.ps/ |
| Cookies Not Marked as Secure | 1 | 低危 | - | https://www.cwa.pna.ps/ |
| Cookies with missing, inconsistent or contradictory properties | 1 | 低危 | - | https://www.cwa.pna.ps/ |
| Insecure Frame (External) | 1 | 低危 | - | https://www.cwa.pna.ps/students/guidance |
| Missing Content-Type Header | 1 | 低危 | - | https://www.cwa.pna.ps/ |
| Programming Error Messages | 1 | 低危 | - | https://www.cwa.pna.ps/ |
| Symfony debug mode enabled | 3 | 低危 | - | https://www.cwa.pna.ps/ads/30, https://www.cwa.pna.ps/contact/save, https://www.cwa.pna.ps/students/... |
| TRACE Method enabled | 1 | 低危 | - | https://www.cwa.pna.ps/ |
| Version Disclosure (PHP) | 1 | 低危 | - | https://www.cwa.pna.ps/ |
| [Possible] Internal IP Address Disclosure | 1 | 低危 | - | https://www.cwa.pna.ps/ |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | https://www.cwa.pna.ps/ |
| Insecure Transportation Security Protocol Supported (TLS 1.1) | 1 | 信息 | - | https://www.cwa.pna.ps/ |
| Outdated JavaScript libraries | 3 | 信息 | - | https://www.cwa.pna.ps/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | https://www.cwa.pna.ps/ |
| Subresource Integrity (SRI) Not Implemented | 1 | 信息 | - | https://www.cwa.pna.ps/ |
| [Possible] Internal Path Disclosure (Windows) | 1 | 信息 | - | https://www.cwa.pna.ps/ |

### mowa.pna.ps

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| Cross-site Scripting | 5 | 高危 | - | https://mowa.pna.ps/ |
| SSL Certificate Is About To Expire | 1 | 中危 | - | https://mowa.pna.ps/ |
| Virtual host directory listing | 1 | 中危 | - | https://mowa.pna.ps/ |
| Cross site scripting (requiring unencoded quote) | 21 | 低危 | - | https://mowa.pna.ps/, https://mowa.pna.ps/ar/, https://mowa.pna.ps/ar/search, https://mowa.pna.ps/ar... |
| Insecure Frame (External) | 2 | 低危 | - | https://mowa.pna.ps/cp, https://mowa.pna.ps/en/contact-us |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | https://mowa.pna.ps/ |
| Outdated JavaScript libraries | 1 | 信息 | - | https://mowa.pna.ps/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | https://mowa.pna.ps/ |
| Subresource Integrity (SRI) Not Implemented | 1 | 信息 | - | https://mowa.pna.ps/ |

### enext.iptime.org:20080

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| Cross-site Scripting | 16 | 高危 | - | http://enext.iptime.org:20080/, http://enext.iptime.org:20080/emp/M010101, http://enext.iptime.org:2... |
| Insecure HTTP Usage | 1 | 中危 | - | http://enext.iptime.org:20080/ |
| SSL/TLS Not Implemented | 1 | 中危 | - | http://enext.iptime.org:20080/ |
| Vulnerable JavaScript libraries | 1 | 中危 | - | http://enext.iptime.org:20080/ |
| jQuery Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') Vulnerability | 3 | 中危 | CVE-2015-9251, CVE-2020-11022, CVE-2020-11023 | http://enext.iptime.org:20080/ |
| jQuery Improperly Controlled Modification of Object Prototype Attributes ('Prototype Pollution') Vulnerability | 1 | 中危 | CVE-2019-11358 | http://enext.iptime.org:20080/ |
| Cookies with missing, inconsistent or contradictory properties | 1 | 低危 | - | http://enext.iptime.org:20080/ |
| Missing Content-Type Header | 1 | 低危 | - | http://enext.iptime.org:20080/ |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | http://enext.iptime.org:20080/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | http://enext.iptime.org:20080/ |
| Subresource Integrity (SRI) Not Implemented | 1 | 信息 | - | http://enext.iptime.org:20080/ |

### mpwh.pna.ps

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| SQL Injection | 2 | 严重 | - | https://mpwh.pna.ps/admin/, https://mpwh.pna.ps/viewpage.aspx |
| Database User Has Admin Privileges | 1 | 高危 | - | https://mpwh.pna.ps/viewpage.aspx |
| Insecure Transportation Security Protocol Supported (TLS 1.0) | 1 | 高危 | - | https://mpwh.pna.ps/ |
| HTTP Strict Transport Security (HSTS) Policy Not Enabled | 1 | 中危 | - | https://mpwh.pna.ps/ |
| TLS/SSL Sweet32 attack | 1 | 中危 | CVE-2016-2183, CVE-2016-6329 | https://mpwh.pna.ps/ |
| TLS/SSL Weak Cipher Suites | 1 | 中危 | - | https://mpwh.pna.ps/ |
| Vulnerable JavaScript libraries | 1 | 中危 | - | https://mpwh.pna.ps/ |
| jQuery Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') Vulnerability | 3 | 中危 | CVE-2020-11022, CVE-2020-11023, CVE-2020-23064 | https://mpwh.pna.ps/ |
| jQuery Improperly Controlled Modification of Object Prototype Attributes ('Prototype Pollution') Vulnerability | 1 | 中危 | CVE-2019-11358 | https://mpwh.pna.ps/ |
| Insecure Frame (External) | 1 | 低危 | - | https://mpwh.pna.ps/ |
| Microsoft IIS tilde directory enumeration | 1 | 低危 | - | https://mpwh.pna.ps/ |
| Possible sensitive directories | 1 | 低危 | - | https://mpwh.pna.ps/ |
| Version Disclosure (ASP.NET) | 1 | 低危 | - | https://mpwh.pna.ps/ |
| Version Disclosure (IIS) | 1 | 低危 | - | https://mpwh.pna.ps/ |
| ViewsState is not Encrypted | 1 | 低危 | - | https://mpwh.pna.ps/ |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | https://mpwh.pna.ps/ |
| Insecure Transportation Security Protocol Supported (TLS 1.1) | 1 | 信息 | - | https://mpwh.pna.ps/ |
| Javascript Source map detected | 1 | 信息 | - | https://mpwh.pna.ps/ |
| Outdated JavaScript libraries | 3 | 信息 | - | https://mpwh.pna.ps/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | https://mpwh.pna.ps/ |
| Subresource Integrity (SRI) Not Implemented | 1 | 信息 | - | https://mpwh.pna.ps/ |
| TLS/SSL (EC)DHE Key Reuse | 1 | 信息 | - | https://mpwh.pna.ps/ |

### coding.iptime.org

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| [Possible] Backup Source Code Detected | 1 | 高危 | - | http://coding.iptime.org/index.php.save |
| Directory listings | 1 | 中危 | - | http://coding.iptime.org/ |
| Insecure HTTP Usage | 1 | 中危 | - | http://coding.iptime.org/ |
| Password transmitted over HTTP | 1 | 中危 | - | http://coding.iptime.org/ |
| SSL/TLS Not Implemented | 1 | 中危 | - | http://coding.iptime.org/ |
| Source Code Disclosure | 2 | 中危 | - | http://coding.iptime.org/ |
| Vulnerable JavaScript libraries | 1 | 中危 | - | http://coding.iptime.org/ |
| jQuery Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') Vulnerability | 3 | 中危 | CVE-2020-11022, CVE-2020-11023, CVE-2020-23064 | http://coding.iptime.org/ |
| jQuery Improperly Controlled Modification of Object Prototype Attributes ('Prototype Pollution') Vulnerability | 1 | 中危 | CVE-2019-11358 | http://coding.iptime.org/ |
| Insecure Frame (External) | 1 | 低危 | - | http://coding.iptime.org/bank/bank_list_object.html |
| Missing Content-Type Header | 1 | 低危 | - | http://coding.iptime.org/ |
| Possible sensitive directories | 1 | 低危 | - | http://coding.iptime.org/ |
| Sensitive pages could be cached | 1 | 低危 | - | http://coding.iptime.org/ |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | http://coding.iptime.org/ |
| Error page web server version disclosure | 1 | 信息 | - | http://coding.iptime.org/ |
| File Upload Functionality Detected | 1 | 信息 | - | http://coding.iptime.org/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | http://coding.iptime.org/ |
| Subresource Integrity (SRI) Not Implemented | 1 | 信息 | - | http://coding.iptime.org/ |
| [Possible] Internal Path Disclosure (*nix) | 1 | 信息 | - | http://coding.iptime.org/ |

### formations.molg.pna.ps

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| SQL Injection | 1 | 严重 | - | http://formations.molg.pna.ps/Admin/Login.aspx |
| Database User Has Admin Privileges | 1 | 高危 | - | http://formations.molg.pna.ps/Admin/Login.aspx |
| Insecure HTTP Usage | 1 | 中危 | - | http://formations.molg.pna.ps/ |
| Password transmitted over HTTP | 1 | 中危 | - | http://formations.molg.pna.ps/ |
| SSL/TLS Not Implemented | 1 | 中危 | - | http://formations.molg.pna.ps/ |
| Cookies with missing, inconsistent or contradictory properties | 1 | 低危 | - | http://formations.molg.pna.ps/ |
| Microsoft IIS tilde directory enumeration | 1 | 低危 | - | http://formations.molg.pna.ps/ |
| Version Disclosure (ASP.NET) | 1 | 低危 | - | http://formations.molg.pna.ps/ |
| Version Disclosure (IIS) | 1 | 低危 | - | http://formations.molg.pna.ps/ |
| ViewsState is not Encrypted | 1 | 低危 | - | http://formations.molg.pna.ps/ |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | http://formations.molg.pna.ps/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | http://formations.molg.pna.ps/ |
| WebDAV Enabled | 1 | 信息 | - | http://formations.molg.pna.ps/ |

### gmdh.gov.ct.tr

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| Insecure Transportation Security Protocol Supported (TLS 1.0) | 1 | 高危 | - | https://gmdh.gov.ct.tr/ |
| SSL Certificate Name Hostname Mismatch | 1 | 中危 | - | https://gmdh.gov.ct.tr/ |
| TLS/SSL Sweet32 attack | 1 | 中危 | CVE-2016-2183, CVE-2016-6329 | https://gmdh.gov.ct.tr/ |
| TLS/SSL Weak Cipher Suites | 1 | 中危 | - | https://gmdh.gov.ct.tr/ |
| Microsoft IIS Server service.cnf file found | 1 | 低危 | - | https://gmdh.gov.ct.tr/_vti_pvt/service.cnf |
| Missing Content-Type Header | 1 | 低危 | - | https://gmdh.gov.ct.tr/ |
| Version Disclosure (IIS) | 1 | 低危 | - | https://gmdh.gov.ct.tr/ |
| Insecure Transportation Security Protocol Supported (TLS 1.1) | 1 | 信息 | - | https://gmdh.gov.ct.tr/ |
| Microsoft Frontpage configuration information | 1 | 信息 | - | https://gmdh.gov.ct.tr/_vti_inf.html |

### trc.synology.me:8000

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| Insecure HTTP Usage | 1 | 中危 | - | http://trc.synology.me:8000/ |
| SSL/TLS Not Implemented | 1 | 中危 | - | http://trc.synology.me:8000/ |
| Cookies with missing, inconsistent or contradictory properties | 1 | 低危 | - | http://trc.synology.me:8000/ |
| Version Disclosure (ASP.NET) | 1 | 低危 | - | http://trc.synology.me:8000/ |
| Version Disclosure (IIS) | 1 | 低危 | - | http://trc.synology.me:8000/ |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | http://trc.synology.me:8000/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | http://trc.synology.me:8000/ |

### tspdj.iptime.org:8110

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| Golang runtime profiling data | 1 | 中危 | - | http://tspdj.iptime.org:8110/ |
| Insecure HTTP Usage | 1 | 中危 | - | http://tspdj.iptime.org:8110/ |
| SSL/TLS Not Implemented | 1 | 中危 | - | http://tspdj.iptime.org:8110/ |
| Unrestricted access to Prometheus | 1 | 低危 | - | http://tspdj.iptime.org:8110/ |
| Unrestricted access to Prometheus Metrics | 1 | 低危 | - | http://tspdj.iptime.org:8110/ |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | http://tspdj.iptime.org:8110/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | http://tspdj.iptime.org:8110/ |

### yne.iptime.org:8000

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| Insecure HTTP Usage | 1 | 中危 | - | http://yne.iptime.org:8000/ |
| Password transmitted over HTTP | 1 | 中危 | - | http://yne.iptime.org:8000/ |
| SSL/TLS Not Implemented | 1 | 中危 | - | http://yne.iptime.org:8000/ |
| TRACE Method enabled | 1 | 低危 | - | http://yne.iptime.org:8000/ |
| Version Disclosure (PHP) | 1 | 低危 | - | http://yne.iptime.org:8000/ |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | http://yne.iptime.org:8000/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | http://yne.iptime.org:8000/ |

### spsun1.iptime.org:8083

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| Insecure HTTP Usage | 1 | 中危 | - | http://spsun1.iptime.org:8083/ |
| SSL/TLS Not Implemented | 1 | 中危 | - | http://spsun1.iptime.org:8083/ |
| Clickjacking: CSP frame-ancestors missing | 1 | 低危 | - | http://spsun1.iptime.org:8083/ |
| An Unsafe Content Security Policy (CSP) Directive in Use | 1 | 信息 | - | http://spsun1.iptime.org:8083/ |
| Missing object-src in CSP Declaration | 1 | 信息 | - | http://spsun1.iptime.org:8083/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | http://spsun1.iptime.org:8083/ |

### ocpu.survey.stat.gov.ct.tr

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| HTTP Strict Transport Security (HSTS) Policy Not Enabled | 1 | 中危 | - | https://ocpu.survey.stat.gov.ct.tr/ |
| SSL Certificate Is About To Expire | 1 | 中危 | - | https://ocpu.survey.stat.gov.ct.tr/ |
| TLS/SSL Weak Cipher Suites | 1 | 中危 | - | https://ocpu.survey.stat.gov.ct.tr/ |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | https://ocpu.survey.stat.gov.ct.tr/ |
| Error page web server version disclosure | 1 | 信息 | - | https://ocpu.survey.stat.gov.ct.tr/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | https://ocpu.survey.stat.gov.ct.tr/ |

### spsun.iptime.org:8117

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| Insecure HTTP Usage | 1 | 中危 | - | http://spsun.iptime.org:8117/ |
| SSL/TLS Not Implemented | 1 | 中危 | - | http://spsun.iptime.org:8117/ |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | http://spsun.iptime.org:8117/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | http://spsun.iptime.org:8117/ |

### tasdik.maliye.gov.ct.tr

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| HTTP Strict Transport Security (HSTS) Policy Not Enabled | 1 | 中危 | - | https://tasdik.maliye.gov.ct.tr/ |
| Content Security Policy (CSP) Not Implemented | 1 | 信息 | - | https://tasdik.maliye.gov.ct.tr/ |
| Generic Email Address Disclosure | 1 | 信息 | - | https://tasdik.maliye.gov.ct.tr/ |
| Permissions-Policy header not implemented | 1 | 信息 | - | https://tasdik.maliye.gov.ct.tr/ |

### www.ssd.gov.ct.tr

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| SSL/TLS Not Implemented | 1 | 中危 | - | http://www.ssd.gov.ct.tr/ |
| Version Disclosure (IIS) | 1 | 低危 | - | http://www.ssd.gov.ct.tr/ |

### cyeng.iptime.org:861

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| 无漏洞 | 0 | - | - | - |

### gsfmc-webdc.iptime.org:31080

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| 无漏洞 | 0 | - | - | - |

### wow2020.iptime.org

| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| 无漏洞 | 0 | - | - | - |

## 3. 严重+高危漏洞统计表

| 漏洞类型 | 影响站点数 | 漏洞总数 | 主要CVE编号 | 代表性站点 |
|----------|------------|----------|-------------|------------|
| SQL注入 | 3 | 10 | CVE-2008-6852, CVE-2009-1499, CVE-2010-2679... | formations.molg.pna.ps, gazimagusa.gov.ct.tr... |
| 信息泄露 | 2 | 6 | CVE-2020-15698, CVE-2020-35610, CVE-2020-35611... | gazimagusa.gov.ct.tr, tspdj.iptime.org:8110 |
| 拒绝服务(DoS) | 1 | 1 | CVE-2021-26036 | gazimagusa.gov.ct.tr |

## 4. 中危+低危+信息类漏洞统计表

| 漏洞类型 | 影响站点数 | 漏洞总数 | 主要CVE编号 | 代表性站点 |
|----------|------------|----------|-------------|------------|
| 其他安全问题 | 15 | 151 | CVE-2006-1957, CVE-2020-15697, CVE-2020-15698... | coding.iptime.org, enext.iptime.org:20080... |
| 跨站脚本(XSS) | 7 | 83 | CVE-2015-9251, CVE-2016-10735, CVE-2018-14040... | coding.iptime.org, enext.iptime.org:20080... |
| 安全配置 | 15 | 49 | - | coding.iptime.org, enext.iptime.org:20080... |
| TLS/SSL配置 | 16 | 33 | CVE-2016-2183, CVE-2016-6329 | coding.iptime.org, enext.iptime.org:20080... |
| JavaScript库 | 7 | 32 | CVE-2019-11358, CVE-2021-21252, CVE-2021-43306... | coding.iptime.org, enext.iptime.org:20080... |
| HTTP配置 | 13 | 22 | CVE-2020-24598, CVE-2022-23798 | coding.iptime.org, enext.iptime.org:20080... |
| 跨站请求伪造(CSRF) | 1 | 10 | CVE-2020-15695, CVE-2020-15700, CVE-2020-35615... | gazimagusa.gov.ct.tr |


